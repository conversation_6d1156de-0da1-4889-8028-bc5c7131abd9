package com.yuedu.teaching.query;

import lombok.Data;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;


/**
* 门店课件表
*
* <AUTHOR>
* @date  2025/08/05
*/
@Data
@Schema(description = "门店课件表查询对象")
public class StoreCoursewareDataQuery {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Integer id;

    /**
     * 资料表ID
     */
    @Schema(description = "资料表ID")
    private Integer coursewareDataId;

    /**
     * 版本(创建副本时总部课件的版本)
     */
    @Schema(description = "版本(创建副本时总部课件的版本)")
    private Integer version;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String coursewareName;

    /**
     * 关联课件ID
     */
    @Schema(description = "关联课件ID")
    private Integer coursewareId;

    /**
     * 关联课件模版ID
     */
    @Schema(description = "关联课件模版ID")
    private Integer dataTemplateId;

    /**
     * 类型 课件类型 与课件模版一致
     */
    @Schema(description = "类型 课件类型 与课件模版一致")
    private Byte type;

    /**
     * 详细数据
     */
    @Schema(description = "详细数据")
    private Object details;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private Integer storeId;

    /**
     * 校区id
     */
    @Schema(description = "校区id")
    private Integer schoolId;

    /**
     * 所属者(副本创建人)
     */
    @Schema(description = "所属者(副本创建人)")
    private Long owner;

    /**
     * 是否使用:0-未使用;1-使用
     */
    @Schema(description = "是否使用:0-未使用;1-使用")
    private Integer isUse;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Byte delFlag;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

}
