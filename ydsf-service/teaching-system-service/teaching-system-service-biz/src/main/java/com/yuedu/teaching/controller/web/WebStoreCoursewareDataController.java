package com.yuedu.teaching.controller.web;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.xss.core.XssCleanIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import jakarta.validation.Valid;
import com.yuedu.teaching.service.StoreCoursewareDataService;
import com.yuedu.teaching.query.StoreCoursewareDataQuery;
import com.yuedu.teaching.query.StoreCoursewareDataStepDetailsQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataDTO;
import com.yuedu.teaching.dto.StoreCoursewareDataStepDetailsDTO;
import com.yuedu.teaching.dto.StoreCoursewareDataStatusDTO;
import com.yuedu.teaching.dto.StoreCoursewareRestoreDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataVO;
import com.yuedu.teaching.vo.StepVO;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;
import com.yuedu.teaching.dto.CoursewareCopyDTO;
import com.yuedu.teaching.valid.StoreCoursewareDataStepDetailsValidGroup;
import com.yuedu.teaching.valid.StoreCoursewareDataValidGroup;

import java.io.Serializable;
import java.util.List;

/**
 * 门店课件表控制层
 *
 * <AUTHOR>
 * @date 2025/08/05
 */

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/storeCoursewareData")
@Tag(description = "store_courseware_data", name = "门店课件表")
@StorePermission
public class WebStoreCoursewareDataController {


    private final StoreCoursewareDataService storeCoursewareDataService;

    /**
     * 创建课件副本
     *
     * @param coursewareCopyDTO 课件副本创建参数
     * @return R<Boolean> 创建结果
     */
    @Operation(summary = "创建课件副本", description = "门店复制当前环节的课件信息，每个账号每个课件环节只能创建一个副本")
    @SysLog("创建课件副本")
    @PostMapping("/createCoursewareCopy")
    public R<Boolean> createCoursewareCopy(@Validated @RequestBody CoursewareCopyDTO coursewareCopyDTO) {
        return R.ok(storeCoursewareDataService.createCoursewareCopy(coursewareCopyDTO));
    }

    /**
     * 查询所有教学环节
     *
     * @param storeCoursewareDataQuery 查询参数
     * @return R<List<StepVO>> 教学环节列表
     */
    @Operation(summary = "查询所有教学环节", description = "查询并展示当前课件下的所有教学环节")
    @GetMapping("/listStoreCoursewareSteps")
    public R<List<StepVO>> listStoreCoursewareSteps(@Validated @ParameterObject StoreCoursewareDataQuery storeCoursewareDataQuery) {
        return R.ok(storeCoursewareDataService.listStoreCoursewareSteps(storeCoursewareDataQuery));
    }

    /**
     * 查询门店课件环节详情
     *
     * @param storeCoursewareDataStepDetailsQuery 查询参数
     * @return R<CoursewareDataStepDetailsVO> 环节详情
     */
    @Operation(summary = "查询门店课件环节详情", description = "查询门店创建的副本课件环节详细信息，确保门店只能查看自己创建的副本课件")
    @GetMapping("/getStoreStepDetails")
    public R<CoursewareDataStepDetailsVO> getStoreStepDetails(
            @Validated(StoreCoursewareDataStepDetailsValidGroup.StoreDataStepDetails.class)
            @ParameterObject StoreCoursewareDataStepDetailsQuery storeCoursewareDataStepDetailsQuery) {
        return R.ok(storeCoursewareDataService.getStoreStepDetails(storeCoursewareDataStepDetailsQuery));
    }

    /**
     * 编辑保存门店课件环节详情
     *
     * @param storeCoursewareDataStepDetailsDTO 环节详情更新参数（包含submitForUse参数控制是否提交使用）
     * @return R<Boolean> 更新结果
     */
    @Operation(summary = "编辑保存门店课件环节详情", description = "门店编辑修改自己创建的副本课件环节详情。submitForUse=true时为'保存并提交'，将课件设置为使用状态；submitForUse=false或null时仅保存，不改变使用状态")
    @SysLog("编辑保存门店课件环节详情")
    @PutMapping("/updateStoreStepDetails")
    @XssCleanIgnore
    public R<Boolean> updateStoreStepDetails(
            @Validated(StoreCoursewareDataStepDetailsValidGroup.UpdateStoreDataStepDetails.class)
            @RequestBody @Valid StoreCoursewareDataStepDetailsDTO storeCoursewareDataStepDetailsDTO) {
        return R.ok(storeCoursewareDataService.updateStoreStepDetails(storeCoursewareDataStepDetailsDTO));
    }

    /**
     * 更新门店课件使用状态
     *
     * @param storeCoursewareDataStatusDTO 状态更新参数
     * @return R<Boolean> 更新结果
     */
    @Operation(summary = "更新门店课件使用状态", description = "单独控制门店副本课件的启用/禁用状态，确保门店只能修改自己创建的副本课件状态")
    @SysLog("更新门店课件使用状态")
    @PutMapping("/updateStoreCoursewareStatus")
    public R<Boolean> updateStoreCoursewareStatus(
            @Validated(StoreCoursewareDataValidGroup.UpdateStatus.class)
            @RequestBody @Valid StoreCoursewareDataStatusDTO storeCoursewareDataStatusDTO) {
        return R.ok(storeCoursewareDataService.updateStoreCoursewareStatus(storeCoursewareDataStatusDTO));
    }

    /**
     * 恢复门店课件为标准版内容
     *
     * @param storeCoursewareRestoreDTO 恢复参数
     * @return R<Boolean> 恢复结果
     */
    @Operation(summary = "恢复门店课件为标准版内容", description = "将门店副本课件内容恢复为总部标准版最新内容，保持门店副本身份标识不变，仅同步内容数据")
    @SysLog("恢复门店课件为标准版内容")
    @PutMapping("/restoreStoreCoursewareToStandard")
    public R<Boolean> restoreStoreCoursewareToStandard(
            @Validated(StoreCoursewareDataValidGroup.RestoreStandard.class)
            @RequestBody @Valid StoreCoursewareRestoreDTO storeCoursewareRestoreDTO) {
        return R.ok(storeCoursewareDataService.restoreStoreCoursewareToStandard(storeCoursewareRestoreDTO));
    }




}
