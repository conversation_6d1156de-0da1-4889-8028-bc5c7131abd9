package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.teaching.query.StoreCoursewareDataQuery;
import com.yuedu.teaching.query.StoreCoursewareDataStepDetailsQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataDTO;
import com.yuedu.teaching.dto.StoreCoursewareDataStepDetailsDTO;
import com.yuedu.teaching.dto.StoreCoursewareDataStatusDTO;
import com.yuedu.teaching.dto.StoreCoursewareRestoreDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataVO;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;
import com.yuedu.teaching.entity.StoreCoursewareData;
import com.yuedu.teaching.dto.CoursewareCopyDTO;
import com.yuedu.teaching.vo.StepVO;

import java.util.List;

/**
* 门店课件表服务接口
*
* <AUTHOR>
* @date  2025/08/05
*/
public interface StoreCoursewareDataService extends IService<StoreCoursewareData> {



    /**
     * 创建课件副本
     * @param coursewareCopyDTO 课件副本创建参数
     * @return Boolean 创建结果
     */
    Boolean createCoursewareCopy(CoursewareCopyDTO coursewareCopyDTO);

    /**
     * 查询所有教学环节
     * @param storeCoursewareDataQuery 查询参数
     * @return List<StepVO> 教学环节列表
     */
    List<StepVO> listStoreCoursewareSteps(StoreCoursewareDataQuery storeCoursewareDataQuery);

    /**
     * 查询门店课件环节详情
     * @param storeCoursewareDataStepDetailsQuery 查询参数
     * @return CoursewareDataStepDetailsVO 环节详情
     */
    CoursewareDataStepDetailsVO getStoreStepDetails(StoreCoursewareDataStepDetailsQuery storeCoursewareDataStepDetailsQuery);

    /**
     * 更新门店课件环节详情
     * @param storeCoursewareDataStepDetailsDTO 更新参数
     * @return Boolean 更新结果
     */
    Boolean updateStoreStepDetails(StoreCoursewareDataStepDetailsDTO storeCoursewareDataStepDetailsDTO);

    /**
     * 更新门店课件使用状态
     * @param storeCoursewareDataStatusDTO 状态更新参数
     * @return Boolean 更新结果
     */
    Boolean updateStoreCoursewareStatus(StoreCoursewareDataStatusDTO storeCoursewareDataStatusDTO);

    /**
     * 恢复门店课件为标准版内容
     * @param storeCoursewareRestoreDTO 恢复参数
     * @return Boolean 恢复结果
     */
    Boolean restoreStoreCoursewareToStandard(StoreCoursewareRestoreDTO storeCoursewareRestoreDTO);
}
