package com.yuedu.teaching.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.constant.enums.IsUseEnum;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.mapper.StoreCoursewareDataMapper;
import com.yuedu.teaching.mapper.StoreCoursewareDataStepDetailsMapper;
import com.yuedu.teaching.mapper.StoreCoursewareStepMapper;
import com.yuedu.teaching.mapper.CoursewareMapper;
import com.yuedu.teaching.mapper.CoursewareStepMapper;
import com.yuedu.teaching.mapper.TeachingPageTemplateMapper;
import com.yuedu.teaching.service.StoreCoursewareDataService;
import com.yuedu.teaching.service.CoursewareStepPubService;
import com.yuedu.teaching.query.StoreCoursewareDataQuery;
import com.yuedu.teaching.query.StoreCoursewareDataStepDetailsQuery;
import com.yuedu.teaching.query.CoursewareStepQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataDTO;
import com.yuedu.teaching.dto.StoreCoursewareDataStepDetailsDTO;
import com.yuedu.teaching.dto.StoreCoursewareDataStatusDTO;
import com.yuedu.teaching.dto.StoreCoursewareRestoreDTO;
import com.yuedu.teaching.dto.CoursewareCopyDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataVO;
import com.yuedu.teaching.vo.StepVO;
import com.yuedu.teaching.vo.ClientStepDetailsVO;
import com.yuedu.teaching.vo.StepPubVO;
import com.yuedu.teaching.vo.CoursewareDataStepDetailsVO;
import com.yuedu.teaching.entity.StoreCoursewareData;
import com.yuedu.teaching.entity.StoreCoursewareDataStepDetails;
import com.yuedu.teaching.entity.StoreCoursewareStep;
import com.yuedu.teaching.entity.Courseware;
import com.yuedu.teaching.entity.CoursewareStep;
import com.yuedu.teaching.entity.TeachingPageTemplateEntity;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.common.core.util.FileUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Objects;
import java.util.Map;


/**
 * 门店课件表服务层
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Service
@RequiredArgsConstructor
public class StoreCoursewareDataServiceImpl extends ServiceImpl<StoreCoursewareDataMapper, StoreCoursewareData>
        implements StoreCoursewareDataService {

    private final CoursewareStepPubService coursewareStepPubService;
    private final StoreCoursewareDataStepDetailsMapper storeCoursewareDataStepDetailsMapper;
    private final StoreCoursewareStepMapper storeCoursewareStepMapper;
    private final CoursewareMapper coursewareMapper;
    private final CoursewareStepMapper coursewareStepMapper;
    private final TeachingPageTemplateMapper teachingPageTemplateMapper;


    /**
     * 创建课件副本
     *
     * @param coursewareCopyDTO 课件副本创建参数
     * @return Boolean 创建结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createCoursewareCopy(CoursewareCopyDTO coursewareCopyDTO) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        // 检查唯一性：每个账号每个课件环节只能创建一个副本
        boolean exists = this.exists(Wrappers.<StoreCoursewareData>lambdaQuery()
                .eq(StoreCoursewareData::getCoursewareId, coursewareCopyDTO.getCoursewareId())
                .eq(StoreCoursewareData::getCoursewareDataId, coursewareCopyDTO.getCoursewareDataId())
                .eq(StoreCoursewareData::getOwner, userId)
                .eq(StoreCoursewareData::getStoreId, storeId.intValue())
                .eq(StoreCoursewareData::getSchoolId, schoolId.intValue()));

        if (exists) {
            throw new BizException("该课件环节已存在副本，不能重复创建");
        }

        // 调用CoursewareStepPubService.viewCoursewareStep获取课件数据
        CoursewareStepQuery query = new CoursewareStepQuery();
        query.setCoursewareId(coursewareCopyDTO.getCoursewareId());
        query.setCoursewareDataId(coursewareCopyDTO.getCoursewareDataId());

        List<ClientStepDetailsVO> stepDetails = coursewareStepPubService.viewCoursewareStep(query);
        if (stepDetails == null || stepDetails.isEmpty()) {
            throw new BizException("未找到课件数据");
        }

        // 获取课件版本信息
        Courseware courseware = coursewareMapper.selectById(coursewareCopyDTO.getCoursewareId());
        if (courseware == null) {
            throw new BizException("课件不存在");
        }
        Integer version = ObjectUtil.defaultIfNull(courseware.getVersion(), 1);

        return createStoreCoursewareCopy(coursewareCopyDTO, stepDetails, userId, storeId.intValue(), schoolId.intValue(), version);
    }

    /**
     * 查询所有教学环节
     *
     * @param storeCoursewareDataQuery 查询参数
     * @return List<StepVO> 教学环节列表
     */
    @Override
    public List<StepVO> listStoreCoursewareSteps(StoreCoursewareDataQuery storeCoursewareDataQuery) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        // 查询门店课件数据
        List<StoreCoursewareStep> storeSteps = storeCoursewareStepMapper.selectList(
                Wrappers.<StoreCoursewareStep>lambdaQuery()
                        .eq(StoreCoursewareStep::getCoursewareId, storeCoursewareDataQuery.getCoursewareId())
                        .eq(StoreCoursewareStep::getCoursewareDataId, storeCoursewareDataQuery.getCoursewareDataId())
                        .eq(StoreCoursewareStep::getOwner, userId)
                        .eq(StoreCoursewareStep::getStoreId, storeId.intValue())
                        .eq(StoreCoursewareStep::getSchoolId, schoolId.intValue())
                        .orderByAsc(StoreCoursewareStep::getStepOrder)
        );

        if (storeSteps.isEmpty()) {
            return Collections.emptyList();
        }

        // 转换为StepVO并构建树形结构
        return buildStepTree(storeSteps);
    }

    /**
     * 创建门店课件副本数据
     */
    private Boolean createStoreCoursewareCopy(CoursewareCopyDTO coursewareCopyDTO, List<ClientStepDetailsVO> stepDetails,
                                              Long userId, Integer storeId, Integer schoolId, Integer version) {
        try {
            // 1. 创建store_courseware_data记录
            StoreCoursewareData storeCoursewareData = new StoreCoursewareData();
            storeCoursewareData.setCoursewareDataId(coursewareCopyDTO.getCoursewareDataId());
            storeCoursewareData.setVersion(version);
            storeCoursewareData.setCoursewareName(TeachingConstant.COPY_COURSE_WARE_REMAK);
            storeCoursewareData.setCoursewareId(coursewareCopyDTO.getCoursewareId());
            storeCoursewareData.setStoreId(storeId);
            storeCoursewareData.setSchoolId(schoolId);
            storeCoursewareData.setOwner(userId);
            storeCoursewareData.setIsUse(IsUseEnum.IS_USE_0.code);
            this.save(storeCoursewareData);

            // 2. 处理教学环节数据
            ClientStepDetailsVO stepDetailsVO = stepDetails.get(0);
            if (stepDetailsVO.getPages() instanceof List) {
                @SuppressWarnings("unchecked")
                List<StepPubVO> pages = (List<StepPubVO>) stepDetailsVO.getPages();

                for (StepPubVO page : pages) {
                    // 创建store_courseware_step记录
                    StoreCoursewareStep storeCoursewareStep = new StoreCoursewareStep();
                    storeCoursewareStep.setStepId(page.getId());
                    storeCoursewareStep.setCoursewareId(coursewareCopyDTO.getCoursewareId());
                    storeCoursewareStep.setCoursewareDataId(coursewareCopyDTO.getCoursewareDataId());
                    // 从configs中获取stepName
                    String stepName = "";
                    if (page.getConfigs() != null) {
                        try {
                            if (page.getConfigs() instanceof String) {
                                cn.hutool.json.JSONObject configJson = cn.hutool.json.JSONUtil.parseObj((String) page.getConfigs());
                                stepName = configJson.getStr("stepName", "");
                            } else if (page.getConfigs() instanceof cn.hutool.json.JSONObject) {
                                stepName = ((cn.hutool.json.JSONObject) page.getConfigs()).getStr("stepName", "");
                            }
                        } catch (Exception e) {
                            stepName = "默认环节名称";
                        }
                    }
                    storeCoursewareStep.setStepName(stepName);
                    storeCoursewareStep.setStepParent(0); // 根据实际情况设置
                    storeCoursewareStep.setStepOrder(page.getStepOrder());
                    storeCoursewareStep.setVersion(version);
                    storeCoursewareStep.setType(2); // 页面类型
                    storeCoursewareStep.setPageTemplateId(page.getPageTemplateId());
                    storeCoursewareStep.setStoreId(storeId);
                    storeCoursewareStep.setSchoolId(schoolId);
                    storeCoursewareStep.setOwner(userId);
                    storeCoursewareStepMapper.insert(storeCoursewareStep);

                    // 创建store_courseware_data_step_details记录
                    StoreCoursewareDataStepDetails storeStepDetails = new StoreCoursewareDataStepDetails();
                    storeStepDetails.setCoursewareDataStepDetailsId(page.getId());
                    storeStepDetails.setCoursewareName(TeachingConstant.COPY_COURSE_WARE_REMAK);
                    storeStepDetails.setCoursewareId(coursewareCopyDTO.getCoursewareId());
                    storeStepDetails.setCoursewareDataId(coursewareCopyDTO.getCoursewareDataId());
                    storeStepDetails.setStepId(page.getId());
                    storeStepDetails.setDetails(page.getConfigs());
                    storeStepDetails.setTool(page.getTools());
                    storeStepDetails.setPageTemplateId(page.getPageTemplateId());
                    storeStepDetails.setStoreId(storeId);
                    storeStepDetails.setSchoolId(schoolId);
                    storeStepDetails.setOwner(userId);
                    storeCoursewareDataStepDetailsMapper.insert(storeStepDetails);
                }
            }

            return true;
        } catch (Exception e) {
            throw new BizException("创建课件副本失败：" + e.getMessage());
        }
    }

    /**
     * 构建教学环节树形结构
     */
    private List<StepVO> buildStepTree(List<StoreCoursewareStep> storeSteps) {
        List<StepVO> stepVOs = storeSteps.stream()
                .map(step -> {
                    StepVO stepVO = new StepVO();
                    stepVO.setId(step.getId());
                    stepVO.setStepName(step.getStepName());
                    stepVO.setStepParent(step.getStepParent());
                    stepVO.setStepOrder(step.getStepOrder());
                    stepVO.setType(step.getType());
                    stepVO.setPageTemplateId(step.getPageTemplateId());
                    return stepVO;
                })
                .toList();

        // 构建树形结构
        List<StepVO> rootSteps = new ArrayList<>();
        for (StepVO stepVO : stepVOs) {
            if (stepVO.getStepParent().equals(0)) {
                rootSteps.add(stepVO);
            }
        }

        for (StepVO rootStep : rootSteps) {
            List<StepVO> children = getChildren(rootStep.getId(), stepVOs);
            rootStep.setChildren(children);
        }

        return rootSteps;
    }

    /**
     * 递归获取子节点
     */
    private List<StepVO> getChildren(Integer parentId, List<StepVO> allSteps) {
        List<StepVO> children = new ArrayList<>();
        for (StepVO step : allSteps) {
            if (step.getStepParent().equals(parentId)) {
                children.add(step);
            }
        }

        for (StepVO child : children) {
            List<StepVO> grandChildren = getChildren(child.getId(), allSteps);
            child.setChildren(grandChildren);
        }

        return children.isEmpty() ? Collections.emptyList() : children;
    }

    /**
     * 查询门店课件环节详情
     *
     * @param storeCoursewareDataStepDetailsQuery 查询参数
     * @return CoursewareDataStepDetailsVO 环节详情
     */
    @Override
    public CoursewareDataStepDetailsVO getStoreStepDetails(StoreCoursewareDataStepDetailsQuery storeCoursewareDataStepDetailsQuery) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        CoursewareDataStepDetailsVO detailsVO = new CoursewareDataStepDetailsVO();
        BeanUtil.copyProperties(storeCoursewareDataStepDetailsQuery, detailsVO);

        // 查询门店课件环节详情数据，确保只能查看自己创建的副本
        StoreCoursewareDataStepDetails storeEntity = storeCoursewareDataStepDetailsMapper.selectOne(
                Wrappers.<StoreCoursewareDataStepDetails>lambdaQuery()
                        .eq(StoreCoursewareDataStepDetails::getCoursewareId, storeCoursewareDataStepDetailsQuery.getCoursewareId())
                        .eq(StoreCoursewareDataStepDetails::getCoursewareDataId, storeCoursewareDataStepDetailsQuery.getCoursewareDataId())
                        .eq(StoreCoursewareDataStepDetails::getStepId, storeCoursewareDataStepDetailsQuery.getStepId())
                        .eq(StoreCoursewareDataStepDetails::getOwner, userId)
                        .eq(StoreCoursewareDataStepDetails::getStoreId, storeId.intValue())
                        .eq(StoreCoursewareDataStepDetails::getSchoolId, schoolId.intValue())
        );

        if (Objects.isNull(storeEntity)) {
            throw new BizException("环节详情不存在或无权限访问");
        }

        // 查询门店环节信息（而不是原始环节信息）
        StoreCoursewareStep storeCoursewareStep = storeCoursewareStepMapper.selectOne(
                Wrappers.<StoreCoursewareStep>lambdaQuery()
                        .eq(StoreCoursewareStep::getStepId, storeCoursewareDataStepDetailsQuery.getStepId())
                        .eq(StoreCoursewareStep::getCoursewareId, storeCoursewareDataStepDetailsQuery.getCoursewareId())
                        .eq(StoreCoursewareStep::getCoursewareDataId, storeCoursewareDataStepDetailsQuery.getCoursewareDataId())
                        .eq(StoreCoursewareStep::getOwner, userId)
                        .eq(StoreCoursewareStep::getStoreId, storeId.intValue())
                        .eq(StoreCoursewareStep::getSchoolId, schoolId.intValue())
        );

        if (Objects.isNull(storeCoursewareStep)) {
            throw new BizException("门店环节不存在");
        }

        // 处理门店环节详情数据
        JSONObject details = null;
        JSONObject tool = null;
        BeanUtil.copyProperties(storeEntity, detailsVO);
        if (Objects.nonNull(storeEntity.getDetails())) {
            details = JSONUtil.parseObj(storeEntity.getDetails());
            addPathField(details);
            detailsVO.setDetails(details);
        }
        if (Objects.nonNull(storeEntity.getTool())) {
            tool = JSONUtil.parseObj(storeEntity.getTool());
            addPathField(tool);
            detailsVO.setTool(tool);
        }

        // 设置门店环节基本信息
        detailsVO.setStepName(storeCoursewareStep.getStepName());

        // 如果是环节类型，直接返回
        if (storeCoursewareStep.getType() == TeachingConstant.COURSEWARE_STEP_TYPE_LINK) {
            return detailsVO;
        }

        // 查询页面模版信息（仍需要从模版表获取viewUrl和remark等模版固定信息）
        TeachingPageTemplateEntity teachingPageTemplate = teachingPageTemplateMapper.selectById(storeCoursewareStep.getPageTemplateId());
        if (Objects.isNull(teachingPageTemplate)) {
            throw new BizException("模版不存在");
        }

        detailsVO.setViewUrl(teachingPageTemplate.getViewUrl());
        detailsVO.setRemark(teachingPageTemplate.getRemark());

        // 处理模版详情数据
        JSONObject templateDetails = JSONUtil.parseObj(teachingPageTemplate.getAttr());
        addPathField(templateDetails);
        setStepDetails(detailsVO, details, templateDetails);

        // 设置模版相关信息（优先使用门店数据，如果门店数据为空则使用模版数据）
        if (Objects.isNull(storeEntity.getPageTemplateId())) {
            detailsVO.setPageTemplateId(teachingPageTemplate.getId());
        }
        if (Objects.isNull(storeEntity.getTeachingPageCategory())) {
            detailsVO.setTeachingPageCategory(teachingPageTemplate.getCategory());
        }
        if (Objects.isNull(storeEntity.getTeachingPageType())) {
            detailsVO.setTeachingPageType(teachingPageTemplate.getType());
        }

        return detailsVO;
    }

    /**
     * 合并两个对象的值
     *
     * @param detailsVO       返回值
     * @param details         环节数据
     * @param templateDetails 模版数据
     */
    private void setStepDetails(CoursewareDataStepDetailsVO detailsVO, JSONObject details, JSONObject templateDetails) {
        // 创建一个新的 JSONObject 来存储合并后的结果
        JSONObject mergedDetails = new JSONObject();

        if (Objects.nonNull(templateDetails)) {
            // 先将 templateDetails 的内容合并到 mergedDetails 中
            mergedDetails.putAll(templateDetails);
        }

        if (Objects.nonNull(details)) {
            // 再将 details 的内容合并到 mergedDetails 中，这样可以覆盖相同的字段
            mergedDetails.putAll(details);
        }

        // 将合并后的 JSONObject 设置到 detailsVO 中
        detailsVO.setDetails(mergedDetails);
    }

    /**
     * 处理 details，添加 path 字段
     *
     * @param details 详细数据
     */
    public static void addPathField(JSONObject details) {
        addPathFieldRecursive(details);
    }

    private static void addPathFieldRecursive(JSONObject jsonObject) {
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            Object fieldValue = entry.getValue();

            if (fieldValue instanceof cn.hutool.json.JSONArray jsonArray) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    Object item = jsonArray.get(i);
                    if (item instanceof JSONObject jsonObjectItem) {
                        addPathFieldRecursive(jsonObjectItem);
                    }
                }
            } else if (fieldValue instanceof JSONObject jsonObjectItem) {
                addPathFieldRecursive(jsonObjectItem);
            } else if (fieldValue instanceof Map map) {
                // 处理 Map 类型，确保兼容性
                JSONObject nestedJsonObject = JSONUtil.parseObj(map);
                addPathFieldRecursive(nestedJsonObject);
            }
        }

        // 添加 path 字段到当前 JSONObject
        String type = jsonObject.getStr("type");
        if ("image".equals(type) || "video".equals(type) || "audio".equals(type)) {
            String url = jsonObject.getStr("url");
            if (url != null) {
                jsonObject.set("path", FileUtils.completeUrl(url));
            }
        }
    }

    /**
     * 更新门店课件环节详情
     *
     * @param storeCoursewareDataStepDetailsDTO 更新参数
     * @return Boolean 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStoreStepDetails(StoreCoursewareDataStepDetailsDTO storeCoursewareDataStepDetailsDTO) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        // 查询要更新的门店环节详情记录，确保只能更新自己创建的副本
        StoreCoursewareDataStepDetails existingEntity = storeCoursewareDataStepDetailsMapper.selectOne(
                Wrappers.<StoreCoursewareDataStepDetails>lambdaQuery()
                        .eq(StoreCoursewareDataStepDetails::getId, storeCoursewareDataStepDetailsDTO.getId())
                        .eq(StoreCoursewareDataStepDetails::getCoursewareId, storeCoursewareDataStepDetailsDTO.getCoursewareId())
                        .eq(StoreCoursewareDataStepDetails::getCoursewareDataId, storeCoursewareDataStepDetailsDTO.getCoursewareDataId())
                        .eq(StoreCoursewareDataStepDetails::getStepId, storeCoursewareDataStepDetailsDTO.getStepId())
                        .eq(StoreCoursewareDataStepDetails::getOwner, userId)
                        .eq(StoreCoursewareDataStepDetails::getStoreId, storeId.intValue())
                        .eq(StoreCoursewareDataStepDetails::getSchoolId, schoolId.intValue())
        );

        if (Objects.isNull(existingEntity)) {
            throw new BizException("环节详情不存在或无权限修改");
        }

        // 验证门店环节是否存在
        StoreCoursewareStep storeCoursewareStep = storeCoursewareStepMapper.selectOne(
                Wrappers.<StoreCoursewareStep>lambdaQuery()
                        .eq(StoreCoursewareStep::getStepId, storeCoursewareDataStepDetailsDTO.getStepId())
                        .eq(StoreCoursewareStep::getCoursewareId, storeCoursewareDataStepDetailsDTO.getCoursewareId())
                        .eq(StoreCoursewareStep::getCoursewareDataId, storeCoursewareDataStepDetailsDTO.getCoursewareDataId())
                        .eq(StoreCoursewareStep::getOwner, userId)
                        .eq(StoreCoursewareStep::getStoreId, storeId.intValue())
                        .eq(StoreCoursewareStep::getSchoolId, schoolId.intValue())
        );

        if (Objects.isNull(storeCoursewareStep)) {
            throw new BizException("门店环节不存在");
        }

        // 更新环节详情数据
        StoreCoursewareDataStepDetails updateEntity = new StoreCoursewareDataStepDetails();
        updateEntity.setId(storeCoursewareDataStepDetailsDTO.getId());

        // 只更新允许修改的字段
        if (Objects.nonNull(storeCoursewareDataStepDetailsDTO.getDetails())) {
            updateEntity.setDetails(storeCoursewareDataStepDetailsDTO.getDetails());
        }
        if (Objects.nonNull(storeCoursewareDataStepDetailsDTO.getTool())) {
            updateEntity.setTool(storeCoursewareDataStepDetailsDTO.getTool());
        }

        boolean result = storeCoursewareDataStepDetailsMapper.updateById(updateEntity) > 0;

        if (result && Boolean.TRUE.equals(storeCoursewareDataStepDetailsDTO.getSubmitForUse())) {
            // 只有在"保存并提交"时才将门店课件设置为使用状态
            StoreCoursewareData storeCoursewareData = new StoreCoursewareData();
            storeCoursewareData.setIsUse(IsUseEnum.IS_USE_0.code);

            this.update(storeCoursewareData, Wrappers.<StoreCoursewareData>lambdaUpdate()
                    .eq(StoreCoursewareData::getCoursewareId, storeCoursewareDataStepDetailsDTO.getCoursewareId())
                    .eq(StoreCoursewareData::getCoursewareDataId, storeCoursewareDataStepDetailsDTO.getCoursewareDataId())
                    .eq(StoreCoursewareData::getOwner, userId)
                    .eq(StoreCoursewareData::getStoreId, storeId.intValue())
                    .eq(StoreCoursewareData::getSchoolId, schoolId.intValue())
            );
        }

        return result;
    }

    /**
     * 更新门店课件使用状态
     *
     * @param storeCoursewareDataStatusDTO 状态更新参数
     * @return Boolean 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStoreCoursewareStatus(StoreCoursewareDataStatusDTO storeCoursewareDataStatusDTO) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        // 查询要更新的门店课件记录，确保只能更新自己创建的副本
        StoreCoursewareData existingEntity = this.getOne(
                Wrappers.<StoreCoursewareData>lambdaQuery()
                        .eq(StoreCoursewareData::getCoursewareId, storeCoursewareDataStatusDTO.getCoursewareId())
                        .eq(StoreCoursewareData::getCoursewareDataId, storeCoursewareDataStatusDTO.getCoursewareDataId())
                        .eq(StoreCoursewareData::getOwner, userId)
                        .eq(StoreCoursewareData::getStoreId, storeId.intValue())
                        .eq(StoreCoursewareData::getSchoolId, schoolId.intValue())
        );

        if (Objects.isNull(existingEntity)) {
            throw new BizException("门店课件不存在或无权限修改");
        }

        // 更新使用状态
        StoreCoursewareData updateEntity = new StoreCoursewareData();
        updateEntity.setIsUse(storeCoursewareDataStatusDTO.getIsUse());

        boolean result = this.update(updateEntity, Wrappers.<StoreCoursewareData>lambdaUpdate()
                .eq(StoreCoursewareData::getCoursewareId, storeCoursewareDataStatusDTO.getCoursewareId())
                .eq(StoreCoursewareData::getCoursewareDataId, storeCoursewareDataStatusDTO.getCoursewareDataId())
                .eq(StoreCoursewareData::getOwner, userId)
                .eq(StoreCoursewareData::getStoreId, storeId.intValue())
                .eq(StoreCoursewareData::getSchoolId, schoolId.intValue())
        );

        return result;
    }

    /**
     * 恢复门店课件为标准版内容
     *
     * @param storeCoursewareRestoreDTO 恢复参数
     * @return Boolean 恢复结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean restoreStoreCoursewareToStandard(StoreCoursewareRestoreDTO storeCoursewareRestoreDTO) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        // 验证门店课件是否存在且有权限操作
        StoreCoursewareData existingCoursewareData = this.getOne(
                Wrappers.<StoreCoursewareData>lambdaQuery()
                        .eq(StoreCoursewareData::getCoursewareId, storeCoursewareRestoreDTO.getCoursewareId())
                        .eq(StoreCoursewareData::getCoursewareDataId, storeCoursewareRestoreDTO.getCoursewareDataId())
                        .eq(StoreCoursewareData::getOwner, userId)
                        .eq(StoreCoursewareData::getStoreId, storeId.intValue())
                        .eq(StoreCoursewareData::getSchoolId, schoolId.intValue())
        );

        if (Objects.isNull(existingCoursewareData)) {
            throw new BizException("门店课件不存在或无权限操作");
        }

        // 调用CoursewareStepPubService获取标准版最新课件数据
        CoursewareStepQuery query = new CoursewareStepQuery();
        query.setCoursewareId(storeCoursewareRestoreDTO.getCoursewareId());
        query.setCoursewareDataId(storeCoursewareRestoreDTO.getCoursewareDataId());

        List<ClientStepDetailsVO> stepDetails = coursewareStepPubService.viewCoursewareStep(query);
        if (stepDetails == null || stepDetails.isEmpty()) {
            throw new BizException("未找到标准版课件数据");
        }

        // 获取标准版课件版本信息
        Courseware courseware = coursewareMapper.selectById(storeCoursewareRestoreDTO.getCoursewareId());
        if (courseware == null) {
            throw new BizException("课件不存在");
        }
        Integer version = ObjectUtil.defaultIfNull(courseware.getVersion(), 1);

        // 更新门店课件版本信息和状态
        StoreCoursewareData updateCoursewareData = new StoreCoursewareData();
        updateCoursewareData.setVersion(version);
        updateCoursewareData.setIsUse(IsUseEnum.IS_USE_0.code);

        boolean coursewareUpdateResult = this.update(updateCoursewareData, Wrappers.<StoreCoursewareData>lambdaUpdate()
                .eq(StoreCoursewareData::getCoursewareId, storeCoursewareRestoreDTO.getCoursewareId())
                .eq(StoreCoursewareData::getCoursewareDataId, storeCoursewareRestoreDTO.getCoursewareDataId())
                .eq(StoreCoursewareData::getOwner, userId)
                .eq(StoreCoursewareData::getStoreId, storeId.intValue())
                .eq(StoreCoursewareData::getSchoolId, schoolId.intValue())
        );

        if (!coursewareUpdateResult) {
            throw new BizException("更新门店课件信息失败");
        }

        // 处理教学环节详情数据
        ClientStepDetailsVO stepDetailsVO = stepDetails.get(0);
        if (stepDetailsVO.getPages() instanceof List) {
            @SuppressWarnings("unchecked")
            List<StepPubVO> pages = (List<StepPubVO>) stepDetailsVO.getPages();

            for (StepPubVO page : pages) {
                // 更新门店环节详情数据
                StoreCoursewareDataStepDetails updateStepDetails = new StoreCoursewareDataStepDetails();
                updateStepDetails.setDetails(page.getConfigs());
                updateStepDetails.setTool(page.getTools());
                updateStepDetails.setPageTemplateId(page.getPageTemplateId());

                // 更新现有的门店环节详情记录
                boolean stepDetailsUpdateResult = storeCoursewareDataStepDetailsMapper.update(updateStepDetails,
                        Wrappers.<StoreCoursewareDataStepDetails>lambdaUpdate()
                                .eq(StoreCoursewareDataStepDetails::getCoursewareId, storeCoursewareRestoreDTO.getCoursewareId())
                                .eq(StoreCoursewareDataStepDetails::getCoursewareDataId, storeCoursewareRestoreDTO.getCoursewareDataId())
                                .eq(StoreCoursewareDataStepDetails::getStepId, page.getId())
                                .eq(StoreCoursewareDataStepDetails::getOwner, userId)
                                .eq(StoreCoursewareDataStepDetails::getStoreId, storeId.intValue())
                                .eq(StoreCoursewareDataStepDetails::getSchoolId, schoolId.intValue())
                ) > 0;

                if (!stepDetailsUpdateResult) {
                    throw new BizException("更新环节详情失败，环节ID: " + page.getId());
                }

                // 同时更新门店环节信息
                StoreCoursewareStep updateStep = new StoreCoursewareStep();
                updateStep.setVersion(version);
                updateStep.setPageTemplateId(page.getPageTemplateId());

                storeCoursewareStepMapper.update(updateStep,
                        Wrappers.<StoreCoursewareStep>lambdaUpdate()
                                .eq(StoreCoursewareStep::getCoursewareId, storeCoursewareRestoreDTO.getCoursewareId())
                                .eq(StoreCoursewareStep::getCoursewareDataId, storeCoursewareRestoreDTO.getCoursewareDataId())
                                .eq(StoreCoursewareStep::getStepId, page.getId())
                                .eq(StoreCoursewareStep::getOwner, userId)
                                .eq(StoreCoursewareStep::getStoreId, storeId.intValue())
                                .eq(StoreCoursewareStep::getSchoolId, schoolId.intValue())
                );
            }
        }

        return true;
    }

}
